### **Project Brief: Incident Ticket Quality Evaluator**

**Date:** August 31, 2025

---

### **1. Executive Summary**

This project aims to develop a Next.js web application that improves the quality of production incident documentation. The application will allow users to upload a CSV file containing incident ticket details. It will then leverage a reasoning LLM to evaluate each ticket against predefined quality criteria, assigning a rank (from Poor to Excellent) and generating constructive comments for improvement. The final output will be an on-screen, searchable, and paginated results table, as well as a downloadable, enriched CSV file, providing immediate, actionable feedback to foster better incident reporting practices.

### **2. Problem Statement**

When production incidents occur, the quality of the initial ticket is often inconsistent. Users may omit critical details such as the full impact on end-users, the steps already taken, or a clear description of the business impact. This lack of standardized, high-quality information leads to several pain points:

* **Delayed Triage**: Incident managers and responders waste valuable time seeking clarification instead of addressing the issue.
* **Inaccurate Prioritization**: Without a clear understanding of business and user impact, incidents may be prioritized incorrectly.
* **Ineffective Post-mortems**: Poor data quality makes it difficult to analyze root causes and trends over time.

Existing manual processes lack an immediate feedback loop to help users understand what constitutes a "good" incident ticket, leading to the persistence of poor documentation habits.

### **3. Proposed Solution**

The proposed solution is a web application, built using Next.js, that serves as an automated quality review tool for incident tickets. Users will upload a CSV file containing incident details. For each incident, the application will make a call to a reasoning LLM to evaluate the quality of the information provided against a set of predefined criteria. The LLM will return a quality ranking and generate constructive comments for improvement. The application will then display the results in a paginated and searchable on-screen table and produce a new, downloadable CSV file containing all the original data plus two new columns for the "Ranking" and "Review Comments".

### **4. Target Users**

We have identified two primary user segments for this application:

* **Primary User Segment: Incident Creators (Engineers & Support Staff)**
    * **Profile**: These are the individuals on the front lines who identify and document production incidents.
    * **Goal**: They need immediate, clear, and consistent feedback on the quality of their incident tickets. This tool will serve as a learning mechanism to help them improve their reporting skills over time.

* **Secondary User Segment: Incident Managers & Analysts**
    * **Profile**: These users are responsible for overseeing the incident management process, analyzing trends, and driving process improvements.
    * **Goal**: They need an objective way to measure the overall quality of incident documentation. This tool will provide them with data from each upload to identify systemic issues, spot areas for training, and report on the progress of quality improvement initiatives.

### **5. Goals & Success Metrics**

#### **Business Objectives**
* To improve the clarity, completeness, and consistency of incident ticket documentation.
* To reduce the time required for responders and managers to understand the full context and impact of an incident.
* To establish a data-driven baseline for incident documentation quality.

#### **User Success Metrics**
* Incident creators receive actionable feedback that helps them write better tickets.
* Incident managers can efficiently analyze the quality of documentation from a given batch of incidents.

#### **Key Performance Indicators (KPIs)**
* **Average Ticket Quality Score**: We will track the average score assigned by the LLM to all tickets. The primary goal is to see a **measurable increase in the average ticket quality score over a quarter**.

### **6. MVP Scope**

The goal of the Minimum Viable Product (MVP) is to deliver the core value proposition—automated incident ticket quality analysis—as quickly and simply as possible.

#### **Core Features (Must Have)**
* A single-page web application built with Next.js.
* A user interface with a form to upload a CSV file.
* Backend logic to parse the CSV, iterate through each row, and call an LLM API for quality evaluation.
* An on-screen, paginated results table with search and filter functions (by incident id, team, and score).
* On-screen MI generated from the uploaded data, including:
    * Summary stats (Total processed, successful, failed).
    * A chart showing the number of incidents per score, with the ability to filter by one or more teams.
* A function to download the results in a new CSV file.

#### **Out of Scope for MVP**
* User accounts and authentication.
* A history of previously uploaded files or results.
* A historical MI dashboard with monthly trends.
* Direct integration with any ticketing systems (e.g., JIRA, ServiceNow).

### **7. Post-MVP Vision**

* **Phase 2 Features**:
    * **Historical MI Dashboard**: Introduce a database to store results over time, enabling the "monthly trend" chart and more advanced analytics for managers.
    * **User Accounts & History**: Allow users to create accounts to view a history of their uploaded files.
* **Long-term Vision**:
    * **Real-time Integration**: Integrate directly with ticketing systems to provide quality feedback at the moment a ticket is created or updated.

### **8. Technical Considerations**

* **Platform Requirements**: The solution will be a web application.
* **Technology Preferences**: The application must be built using Next.js for both the frontend and backend.
* **Architecture Considerations**: The implementation for the MVP will be stateless (no persistent database). The design should be kept simple and focused on core functionality, avoiding over-engineering.

### **9. Constraints & Assumptions**

#### **Constraints**
* The application must use Next.js.
* The input must be a CSV file with the following columns: `id`, `ImpactDate`, `Service`, `ProblemService`, `Summary`, `BusinessImpact`, `Instructions`, and `TechnicalDetails`.
* The MVP will not have a persistent database.

#### **Key Assumptions**
* The application will integrate with an in-house hosted, OpenAI-compatible LLM API.
* The criteria for ranking ticket quality are sufficient for the MVP.
* For the MVP, users will remain on the page while the file is processing; the application does not need to handle background processing if the user navigates away.

### **10. Risks & Open Questions**

#### **Key Risks**
* **Key Risk - Prompt Engineering for Audience-Specific Clarity**: The primary risk is ensuring the LLM's evaluation and generated comments are tailored to the correct audience. Prompt engineering will require significant tuning to translate the **business impact, latest update, and user instruction** fields into plain English for a non-technical audience (e.g., business stakeholders), while ensuring the **technical details** remain precise for an IT audience.
* **LLM Performance**: The speed and consistency of the in-house LLM API under load are unknown and could impact the user experience.
* **Internal Data Security**: While the LLM is in-house, the application must still handle potentially sensitive incident data according to all internal data security and privacy policies.

### **11. Next Steps**

This Project Brief provides the full context for the Incident Ticket Quality Evaluator. The next step is to hand this off to the Product Manager (PM) to begin creating the detailed Product Requirements Document (PRD).