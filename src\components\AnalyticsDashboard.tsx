'use client';

import React from 'react';
import { EvaluatedTicket } from '@/types';
import { BarChart3, TrendingUp, Users, Zap, AlertTriangle, CheckCircle } from 'lucide-react';

interface AnalyticsDashboardProps {
  results: EvaluatedTicket[];
}

/**
 * Modern analytics dashboard showing incident categorization insights
 */
export const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({ results }) => {
  if (!results || results.length === 0) {
    return null;
  }

  // Calculate statistics
  const totalIncidents = results.length;
  const avgConfidence = results.reduce((sum, r) => sum + r.confidenceScore, 0) / totalIncidents;
  
  // Category distribution
  const categoryStats = results.reduce((acc, result) => {
    const category = result.predictedCategory;
    acc[category] = (acc[category] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // Team distribution
  const teamStats = results.reduce((acc, result) => {
    const team = result.team;
    acc[team] = (acc[team] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // Confidence distribution
  const highConfidence = results.filter(r => r.confidenceScore >= 0.8).length;
  const mediumConfidence = results.filter(r => r.confidenceScore >= 0.6 && r.confidenceScore < 0.8).length;
  const lowConfidence = results.filter(r => r.confidenceScore < 0.6).length;

  // Impact analysis
  const criticalImpact = results.filter(r => 
    r.ticket.businessImpact.toLowerCase().includes('critical') || 
    r.ticket.businessImpact.toLowerCase().includes('high')
  ).length;

  const getCategoryColor = (category: string) => {
    const colors = {
      'Network': 'bg-blue-500',
      'Hardware': 'bg-orange-500',
      'Software': 'bg-green-500',
      'Security': 'bg-red-500',
      'Application': 'bg-purple-500'
    };
    return colors[category as keyof typeof colors] || 'bg-gray-500';
  };

  const getCategoryIcon = (category: string) => {
    const icons = {
      'Network': '🌐',
      'Hardware': '🖥️',
      'Software': '💻',
      'Security': '🔒',
      'Application': '📱'
    };
    return icons[category as keyof typeof icons] || '📋';
  };

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <BarChart3 className="h-8 w-8 text-indigo-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Incidents</p>
              <p className="text-2xl font-bold text-gray-900">{totalIncidents}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Avg Confidence</p>
              <p className="text-2xl font-bold text-gray-900">{(avgConfidence * 100).toFixed(1)}%</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">High Impact</p>
              <p className="text-2xl font-bold text-gray-900">{criticalImpact}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CheckCircle className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">High Confidence</p>
              <p className="text-2xl font-bold text-gray-900">{highConfidence}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Category Distribution */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <BarChart3 className="h-5 w-5 mr-2 text-indigo-600" />
            Category Distribution
          </h3>
          <div className="space-y-4">
            {Object.entries(categoryStats)
              .sort(([,a], [,b]) => b - a)
              .map(([category, count]) => {
                const percentage = (count / totalIncidents) * 100;
                return (
                  <div key={category} className="flex items-center">
                    <div className="flex items-center min-w-0 flex-1">
                      <span className="text-lg mr-2">{getCategoryIcon(category)}</span>
                      <span className="text-sm font-medium text-gray-900 truncate">
                        {category}
                      </span>
                      <span className="ml-2 text-sm text-gray-500">
                        ({count})
                      </span>
                    </div>
                    <div className="ml-4 flex items-center">
                      <div className="w-24 bg-gray-200 rounded-full h-2 mr-2">
                        <div 
                          className={`h-2 rounded-full ${getCategoryColor(category)}`}
                          style={{ width: `${percentage}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium text-gray-900 w-12 text-right">
                        {percentage.toFixed(1)}%
                      </span>
                    </div>
                  </div>
                );
              })}
          </div>
        </div>

        {/* Team Assignment */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Users className="h-5 w-5 mr-2 text-indigo-600" />
            Team Assignment
          </h3>
          <div className="space-y-4">
            {Object.entries(teamStats)
              .sort(([,a], [,b]) => b - a)
              .map(([team, count]) => {
                const percentage = (count / totalIncidents) * 100;
                return (
                  <div key={team} className="flex items-center">
                    <div className="flex items-center min-w-0 flex-1">
                      <span className="text-sm font-medium text-gray-900 truncate">
                        {team}
                      </span>
                      <span className="ml-2 text-sm text-gray-500">
                        ({count})
                      </span>
                    </div>
                    <div className="ml-4 flex items-center">
                      <div className="w-24 bg-gray-200 rounded-full h-2 mr-2">
                        <div 
                          className="h-2 rounded-full bg-indigo-500"
                          style={{ width: `${percentage}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium text-gray-900 w-12 text-right">
                        {percentage.toFixed(1)}%
                      </span>
                    </div>
                  </div>
                );
              })}
          </div>
        </div>
      </div>

      {/* Confidence Analysis */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Zap className="h-5 w-5 mr-2 text-indigo-600" />
          Confidence Analysis
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600 mb-2">{highConfidence}</div>
            <div className="text-sm text-gray-600 mb-2">High Confidence</div>
            <div className="text-xs text-gray-500">≥ 80%</div>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
              <div 
                className="bg-green-500 h-2 rounded-full"
                style={{ width: `${(highConfidence / totalIncidents) * 100}%` }}
              ></div>
            </div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-yellow-600 mb-2">{mediumConfidence}</div>
            <div className="text-sm text-gray-600 mb-2">Medium Confidence</div>
            <div className="text-xs text-gray-500">60% - 79%</div>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
              <div 
                className="bg-yellow-500 h-2 rounded-full"
                style={{ width: `${(mediumConfidence / totalIncidents) * 100}%` }}
              ></div>
            </div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-red-600 mb-2">{lowConfidence}</div>
            <div className="text-sm text-gray-600 mb-2">Low Confidence</div>
            <div className="text-xs text-gray-500">&lt; 60%</div>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
              <div 
                className="bg-red-500 h-2 rounded-full"
                style={{ width: `${(lowConfidence / totalIncidents) * 100}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;
