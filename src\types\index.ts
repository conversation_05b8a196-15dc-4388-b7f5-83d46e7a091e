// Core data structures for incident ticket management

/**
 * Core incident ticket structure with all required fields
 * Based on story requirements for CSV validation
 */
export interface IncidentTicket {
  id: string;
  ImpactDate: string;
  Service: string;
  ProblemService: string;
  Summary: string;
  BusinessImpact: string;
  Instructions: string;
  TechnicalDetails: string;
}

/**
 * File upload request structure
 */
export interface UploadRequest {
  file: File;
}

/**
 * Upload response structure with direct results
 */
export interface UploadResponse {
  success: boolean;
  fileName?: string;
  totalTickets?: number;
  validationErrors?: string[];
  message?: string;
  error?: string;
  evaluatedTickets?: EvaluatedTicket[];
}

/**
 * File validation result structure
 */
export interface FileValidationResult {
  isValid: boolean;
  errors: string[];
  fileSize?: number;
  mimeType?: string;
}

/**
 * CSV parsing result structure
 */
export interface CSVParseResult {
  data: IncidentTicket[];
  errors: string[];
  meta: {
    delimiter: string;
    linebreak: string;
    aborted: boolean;
    truncated: boolean;
    cursor: number;
  };
}

/**
 * Upload progress state for UI
 */


/**
 * Error types for better error handling
 */
export enum UploadErrorType {
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE = 'INVALID_FILE_TYPE',
  MISSING_COLUMNS = 'MISSING_COLUMNS',
  INVALID_CSV_FORMAT = 'INVALID_CSV_FORMAT',
  NETWORK_ERROR = 'NETWORK_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
}

/**
 * Custom error class for upload operations
 */
export class UploadError extends Error {
  constructor(
    public type: UploadErrorType,
    message: string,
    public details?: any
  ) {
    super(message);
    this.name = 'UploadError';
  }
}

/**
 * Evaluated ticket with prediction results
 */
export interface EvaluatedTicket {
  id: string;
  ticket: {
    ticketId: string;
    description: string;
    service: string;
    problemService: string;
    businessImpact: string;
    instructions: string;
    technicalDetails: string;
    priority: string;
    status: string;
    createdDate: string;
    resolvedDate?: string;
  };
  originalCategory: string; // This is actually the service name for reference
  predictedCategory: string; // AI-predicted category (Network, Hardware, etc.)
  matchStatus: 'Evaluated'; // Since we don't have original categories to compare
  confidenceScore: number; // 0-1 confidence score
  team: string;
  evaluatedAt: string;
}