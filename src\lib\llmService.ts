import { IncidentTicket } from '@/types';

/**
 * LLM Evaluation Response Interface
 */
export interface LLMEvaluationResponse {
  predictedCategory: string;
  confidenceScore: number;
  reasoning?: string;
}

/**
 * LLM Service Configuration
 */
interface LLMConfig {
  apiUrl: string;
  apiKey: string;
  timeout?: number;
  maxRetries?: number;
}

/**
 * LLM Service for evaluating incident tickets
 */
export class LLMService {
  private baseURL: string;
  private apiKey: string;
  private timeout: number;
  private maxRetries: number;

  constructor(config: LLMConfig) {
    this.baseURL = config.apiUrl;
    this.apiKey = config.apiKey;
    this.timeout = config.timeout || 30000;
    this.maxRetries = config.maxRetries || 3;
  }

  /**
   * Evaluate a single incident ticket using LLM
   */
  async evaluateTicket(ticket: IncidentTicket): Promise<LLMEvaluationResponse> {
    const prompt = this.buildEvaluationPrompt(ticket);
    
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        // Use the configured model or default
        const model = process.env.LLM_MODEL || 'gpt-3.5-turbo';

        const response = await fetch(this.baseURL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`
          },
          body: JSON.stringify({
            model: model,
            messages: [{
              role: 'user',
              content: prompt
            }],
            temperature: 0.1,
            max_tokens: 500,
            response_format: { type: 'json_object' }
          }),
          signal: AbortSignal.timeout(this.timeout)
        });

        if (!response.ok) {
          const errorText = await response.text().catch(() => 'Unknown error');
          throw new Error(`HTTP ${response.status}: ${response.statusText}. Response: ${errorText}`);
        }

        const data = await response.json();
        const content = data.choices[0]?.message?.content;
        
        if (!content) {
          throw new Error('No content in LLM response');
        }

        const evaluation = JSON.parse(content);
        return {
          predictedCategory: evaluation.category || 'Unknown',
          confidenceScore: Math.min(Math.max(evaluation.confidence || 0.5, 0), 1),
          reasoning: evaluation.reasoning
        };

      } catch (error) {
        console.error(`LLM evaluation attempt ${attempt} failed:`, error);
        
        if (attempt === this.maxRetries) {
          // Return fallback evaluation on final failure
          return this.getFallbackEvaluation(ticket);
        }
        
        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }

    // This should never be reached, but TypeScript requires it
    return this.getFallbackEvaluation(ticket);
  }

  /**
   * Build evaluation prompt for the LLM
   */
  private buildEvaluationPrompt(ticket: IncidentTicket): string {
    return `
You are an expert IT incident categorization system. Analyze the following incident ticket and categorize it into one of these categories: Network, Hardware, Software, Security, or Application.

Incident Details:
- Service: ${ticket.Service}
- Problem Service: ${ticket.ProblemService}
- Summary: ${ticket.Summary}
- Business Impact: ${ticket.BusinessImpact}
- Technical Details: ${ticket.TechnicalDetails}
- Instructions: ${ticket.Instructions}

Please respond with a JSON object containing:
{
  "category": "one of: Network, Hardware, Software, Security, Application",
  "confidence": "confidence score between 0 and 1",
  "reasoning": "brief explanation of your categorization"
}

Focus on the technical details and service information to make an accurate categorization.
    `.trim();
  }

  /**
   * Provide fallback evaluation when LLM fails
   */
  private getFallbackEvaluation(ticket: IncidentTicket): LLMEvaluationResponse {
    // Simple rule-based fallback
    const service = ticket.Service?.toLowerCase() || '';
    const summary = ticket.Summary?.toLowerCase() || '';
    const technicalDetails = ticket.TechnicalDetails?.toLowerCase() || '';
    
    const text = `${service} ${summary} ${technicalDetails}`;
    
    if (text.includes('network') || text.includes('connectivity') || text.includes('dns')) {
      return { predictedCategory: 'Network', confidenceScore: 0.6 };
    } else if (text.includes('hardware') || text.includes('server') || text.includes('disk')) {
      return { predictedCategory: 'Hardware', confidenceScore: 0.6 };
    } else if (text.includes('security') || text.includes('access') || text.includes('authentication')) {
      return { predictedCategory: 'Security', confidenceScore: 0.6 };
    } else if (text.includes('application') || text.includes('app') || text.includes('database')) {
      return { predictedCategory: 'Application', confidenceScore: 0.6 };
    } else {
      return { predictedCategory: 'Software', confidenceScore: 0.5 };
    }
  }
}

/**
 * Mock LLM Service for development/testing
 */
export class MockLLMService {
  async evaluateTicket(ticket: IncidentTicket): Promise<LLMEvaluationResponse> {
    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));

    // Simple rule-based categorization for demo
    const service = ticket.Service?.toLowerCase() || '';
    const summary = ticket.Summary?.toLowerCase() || '';
    const technicalDetails = ticket.TechnicalDetails?.toLowerCase() || '';

    const text = `${service} ${summary} ${technicalDetails}`;

    let category = 'Software';
    let confidence = 0.7;

    if (text.includes('network') || text.includes('connectivity') || text.includes('dns') || text.includes('internet')) {
      category = 'Network';
      confidence = 0.85;
    } else if (text.includes('hardware') || text.includes('server') || text.includes('disk') || text.includes('memory')) {
      category = 'Hardware';
      confidence = 0.8;
    } else if (text.includes('security') || text.includes('access') || text.includes('authentication') || text.includes('password')) {
      category = 'Security';
      confidence = 0.9;
    } else if (text.includes('application') || text.includes('app') || text.includes('database') || text.includes('web')) {
      category = 'Application';
      confidence = 0.75;
    }

    // Add some randomness to make it more realistic
    confidence += (Math.random() - 0.5) * 0.2;
    confidence = Math.min(Math.max(confidence, 0.3), 0.95);

    return {
      predictedCategory: category,
      confidenceScore: confidence,
      reasoning: `Categorized as ${category} based on keywords in service description and technical details.`
    };
  }
}

/**
 * Create LLM service instance with environment configuration
 */
export function createLLMService(): LLMService | MockLLMService {
  const apiUrl = process.env.LLM_API_URL;
  const apiKey = process.env.LLM_API_KEY;
  const useMock = process.env.USE_MOCK_LLM === 'true';

  // Use mock service if explicitly requested or if credentials are missing
  if (useMock || !apiUrl || !apiKey) {
    console.log('[LLM SERVICE] Using mock LLM service for development');
    return new MockLLMService();
  }

  console.log(`[LLM SERVICE] Using real LLM service at ${apiUrl}`);
  return new LLMService({
    apiUrl,
    apiKey,
    timeout: parseInt(process.env.LLM_TIMEOUT || '30000'),
    maxRetries: parseInt(process.env.LLM_MAX_RETRIES || '3')
  });
}

/**
 * Team assignment mapping based on category
 */
export const TEAM_ASSIGNMENTS: Record<string, string> = {
  'Network': 'Infrastructure Team',
  'Hardware': 'Infrastructure Team',
  'Software': 'Development Team',
  'Security': 'Security Team',
  'Application': 'Development Team'
};
