'use client';

import React, { useState } from 'react';
import { EvaluatedTicket } from '@/types';
import { ChevronDown, ChevronUp, Eye, Clock, AlertCircle, CheckCircle2 } from 'lucide-react';

interface IncidentTableProps {
  results: EvaluatedTicket[];
  loading?: boolean;
  error?: string;
}

interface ExpandedRow {
  [key: string]: boolean;
}

/**
 * Modern, comprehensive incident table with expandable rows and rich data display
 */
export const IncidentTable: React.FC<IncidentTableProps> = ({ results, loading, error }) => {
  const [expandedRows, setExpandedRows] = useState<ExpandedRow>({});

  const toggleRow = (ticketId: string) => {
    setExpandedRows(prev => ({
      ...prev,
      [ticketId]: !prev[ticketId]
    }));
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      'Network': 'bg-blue-100 text-blue-800 border-blue-200',
      'Hardware': 'bg-orange-100 text-orange-800 border-orange-200',
      'Software': 'bg-green-100 text-green-800 border-green-200',
      'Security': 'bg-red-100 text-red-800 border-red-200',
      'Application': 'bg-purple-100 text-purple-800 border-purple-200'
    };
    return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  const getConfidenceColor = (score: number) => {
    if (score >= 0.8) return 'bg-green-500';
    if (score >= 0.6) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const getPriorityIcon = (impact: string) => {
    if (impact.toLowerCase().includes('critical') || impact.toLowerCase().includes('high')) {
      return <AlertCircle className="h-4 w-4 text-red-500" />;
    }
    if (impact.toLowerCase().includes('medium')) {
      return <Clock className="h-4 w-4 text-yellow-500" />;
    }
    return <CheckCircle2 className="h-4 w-4 text-green-500" />;
  };

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
          <span className="ml-3 text-gray-600">Processing incidents...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-red-200 p-8">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-red-800 mb-2">Processing Error</h3>
          <p className="text-red-600">{error}</p>
        </div>
      </div>
    );
  }

  if (!results || results.length === 0) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12">
        <div className="text-center">
          <Eye className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-700 mb-2">No Incidents Found</h3>
          <p className="text-gray-500">Upload a CSV file to see incident analysis results.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-indigo-50 to-blue-50 px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Incident Analysis Results</h2>
            <p className="text-sm text-gray-600 mt-1">{results.length} incidents processed</p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-600">
              <span className="font-medium">Average Confidence:</span>{' '}
              {((results.reduce((sum, r) => sum + r.confidenceScore, 0) / results.length) * 100).toFixed(1)}%
            </div>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Incident
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Service
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                AI Category
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Confidence
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Team
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Impact
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {results.map((incident) => (
              <React.Fragment key={incident.id}>
                {/* Main Row */}
                <tr className="hover:bg-gray-50 transition-colors">
                  <td className="px-6 py-4">
                    <div className="flex items-center">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {incident.ticket.ticketId}
                        </div>
                        <div className="text-sm text-gray-500 max-w-xs truncate">
                          {incident.ticket.description}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{incident.ticket.service}</div>
                    <div className="text-xs text-gray-500">{incident.ticket.problemService}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getCategoryColor(incident.predictedCategory)}`}>
                      {incident.predictedCategory}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                        <div 
                          className={`h-2 rounded-full ${getConfidenceColor(incident.confidenceScore)}`}
                          style={{ width: `${incident.confidenceScore * 100}%` }}
                        ></div>
                      </div>
                      <span className="text-xs font-medium">
                        {(incident.confidenceScore * 100).toFixed(1)}%
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-indigo-100 text-indigo-800">
                      {incident.team}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {getPriorityIcon(incident.ticket.businessImpact)}
                      <span className="ml-2 text-sm text-gray-600">
                        {incident.ticket.businessImpact.split(' ')[0]}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      onClick={() => toggleRow(incident.ticket.ticketId)}
                      className="text-indigo-600 hover:text-indigo-900 inline-flex items-center"
                    >
                      {expandedRows[incident.ticket.ticketId] ? (
                        <>
                          <ChevronUp className="h-4 w-4 mr-1" />
                          Less
                        </>
                      ) : (
                        <>
                          <ChevronDown className="h-4 w-4 mr-1" />
                          Details
                        </>
                      )}
                    </button>
                  </td>
                </tr>

                {/* Expanded Row */}
                {expandedRows[incident.ticket.ticketId] && (
                  <tr className="bg-gray-50">
                    <td colSpan={7} className="px-6 py-4">
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div className="space-y-4">
                          <div>
                            <h4 className="text-sm font-medium text-gray-900 mb-2">Business Impact</h4>
                            <p className="text-sm text-gray-700 bg-white p-3 rounded-lg border">
                              {incident.ticket.businessImpact}
                            </p>
                          </div>
                          <div>
                            <h4 className="text-sm font-medium text-gray-900 mb-2">Instructions</h4>
                            <p className="text-sm text-gray-700 bg-white p-3 rounded-lg border">
                              {incident.ticket.instructions}
                            </p>
                          </div>
                        </div>
                        <div className="space-y-4">
                          <div>
                            <h4 className="text-sm font-medium text-gray-900 mb-2">Technical Details</h4>
                            <p className="text-sm text-gray-700 bg-white p-3 rounded-lg border">
                              {incident.ticket.technicalDetails}
                            </p>
                          </div>
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <h4 className="text-sm font-medium text-gray-900 mb-1">Created</h4>
                              <p className="text-sm text-gray-600">
                                {new Date(incident.ticket.createdDate).toLocaleDateString()}
                              </p>
                            </div>
                            <div>
                              <h4 className="text-sm font-medium text-gray-900 mb-1">Status</h4>
                              <p className="text-sm text-gray-600">{incident.ticket.status}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default IncidentTable;
