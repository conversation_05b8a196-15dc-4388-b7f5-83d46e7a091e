import { NextRequest, NextResponse } from 'next/server';
import { UploadResponse, UploadErrorType, EvaluatedTicket } from '@/types';
import { validateFile } from '@/lib/validation';
import { createLLMService, TEAM_ASSIGNMENTS } from '@/lib/llmService';

// Configure maximum file size (10MB)
const MAX_FILE_SIZE = parseInt(process.env.MAX_FILE_SIZE || '10485760'); // 10MB in bytes

export async function POST(request: NextRequest) {
  try {
    // Parse multipart form data
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        {
          success: false,
          error: 'No file provided',
          errorType: UploadErrorType.INVALID_FILE_TYPE
        } as UploadResponse,
        { status: 400 }
      );
    }

    // Validate file properties
    const fileValidation = validateFile(file);
    if (!fileValidation.isValid) {
      return NextResponse.json(
        {
          success: false,
          error: fileValidation.errors.join(', '),
          errorType: UploadErrorType.INVALID_FILE_TYPE
        } as UploadResponse,
        { status: 400 }
      );
    }

    // Parse and validate CSV file
    let csvResult;
    try {
      // Convert File to text for server-side parsing
      const fileText = await file.text();
      
      // Use Papa Parse directly on server side
      const Papa = require('papaparse');
      const parseResult = Papa.parse(fileText, {
        header: true,
        skipEmptyLines: true,
        transformHeader: (header: string) => header.trim()
      });
      
      // Validate headers
      const headers = parseResult.meta.fields || [];
      const { validateCSVHeaders, validateCSVData } = require('@/lib/validation');
      const headerValidation = validateCSVHeaders(headers);
      
      if (!headerValidation.isValid) {
        csvResult = {
          data: [],
          errors: headerValidation.errors,
          meta: parseResult.meta
        };
      } else {
        // Validate data rows
        const { validTickets, errors } = validateCSVData(parseResult.data);
        csvResult = {
          data: validTickets,
          errors,
          meta: parseResult.meta
        };
      }
    } catch (error) {
      console.error('[CSV PARSE ERROR]', error);
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to parse CSV file. Please ensure it is a valid CSV format.',
          errorType: UploadErrorType.INVALID_CSV_FORMAT
        } as UploadResponse,
        { status: 400 }
      );
    }

    // Check if there are validation errors
    if (csvResult.errors.length > 0 && csvResult.data.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: csvResult.errors.join(', '),
          errorType: UploadErrorType.INVALID_CSV_FORMAT
        } as UploadResponse,
        { status: 400 }
      );
    }

    // Process CSV data with real LLM evaluation
    console.log(`[LLM PROCESSING] Starting evaluation of ${csvResult.data.length} tickets`);

    const llmService = createLLMService();

    const evaluatedTickets: EvaluatedTicket[] = [];

    for (let index = 0; index < csvResult.data.length; index++) {
      const row = csvResult.data[index];

      try {
        // Convert row to IncidentTicket format
        const ticket = {
          id: row['id'] || `INC-${String(index + 1).padStart(6, '0')}`,
          ImpactDate: row['ImpactDate'] || new Date().toISOString(),
          Service: row['Service'] || 'Unknown',
          ProblemService: row['ProblemService'] || row['Service'] || 'Unknown',
          Summary: row['Summary'] || `Incident ticket ${index + 1}`,
          BusinessImpact: row['BusinessImpact'] || 'Not specified',
          Instructions: row['Instructions'] || 'No instructions provided',
          TechnicalDetails: row['TechnicalDetails'] || 'No technical details provided'
        };

        // Get LLM evaluation
        const evaluation = await llmService.evaluateTicket(ticket);

        // Note: CSV has no "Category" field - we're doing pure AI categorization
        // The "originalCategory" represents the service type for reference, not comparison
        const originalService = row['Service'] || 'Unknown';

        // Since we don't have original categories to compare against,
        // we'll mark all as "Evaluated" instead of Correct/Incorrect
        const matchStatus = 'Evaluated';

        evaluatedTickets.push({
          id: `eval-${Date.now()}-${index + 1}`,
          originalCategory: originalService, // This is now the service name, not a category
          predictedCategory: evaluation.predictedCategory,
          confidenceScore: parseFloat(evaluation.confidenceScore.toFixed(3)),
          matchStatus: matchStatus as 'Evaluated',
          team: TEAM_ASSIGNMENTS[evaluation.predictedCategory] || 'General Team',
          evaluatedAt: new Date().toISOString(),
          ticket: {
            ticketId: ticket.id,
            description: ticket.Summary,
            service: originalService,
            problemService: ticket.ProblemService,
            businessImpact: ticket.BusinessImpact,
            instructions: ticket.Instructions,
            technicalDetails: ticket.TechnicalDetails,
            priority: 'Medium', // Default priority
            status: 'Open', // Default status
            createdDate: ticket.ImpactDate,
            resolvedDate: undefined
          }
        });

        // Log progress every 10 tickets
        if ((index + 1) % 10 === 0) {
          console.log(`[LLM PROGRESS] Processed ${index + 1}/${csvResult.data.length} tickets`);
        }

      } catch (error) {
        console.error(`[LLM ERROR] Failed to evaluate ticket ${index + 1}:`, error);

        // Add fallback evaluation for failed tickets
        const originalService = row['Service'] || 'Unknown';
        evaluatedTickets.push({
          id: `eval-${Date.now()}-${index + 1}`,
          originalCategory: originalService,
          predictedCategory: 'Software', // Default fallback
          confidenceScore: 0.3,
          matchStatus: 'Evaluated',
          team: 'Development Team',
          evaluatedAt: new Date().toISOString(),
          ticket: {
            ticketId: row['id'] || `INC-${String(index + 1).padStart(6, '0')}`,
            description: row['Summary'] || `Incident ticket ${index + 1}`,
            service: originalService,
            problemService: row['ProblemService'] || originalService,
            businessImpact: row['BusinessImpact'] || 'Not specified',
            instructions: row['Instructions'] || 'No instructions provided',
            technicalDetails: row['TechnicalDetails'] || 'No technical details provided',
            priority: 'Medium',
            status: 'Open',
            createdDate: row['ImpactDate'] ? new Date(row['ImpactDate']).toISOString() : new Date().toISOString(),
            resolvedDate: undefined
          }
        });
      }
    }

    console.log(`[LLM PROCESSING] Completed evaluation of ${evaluatedTickets.length} tickets`);

    const response: UploadResponse = {
      success: true,
      fileName: file.name,
      totalTickets: csvResult.data.length,
      evaluatedTickets,
      validationErrors: csvResult.errors.length > 0 ? csvResult.errors : undefined,
      message: `Successfully processed ${csvResult.data.length} incident tickets with LLM evaluation`
    };

    // Log successful upload
    console.log(`[UPLOAD SUCCESS] File: ${file.name}, Tickets: ${csvResult.data.length}, Evaluated: ${evaluatedTickets.length}`);

    return NextResponse.json(response, { status: 200 });

  } catch (error) {
    // Handle unexpected errors
    console.error('[UPLOAD ERROR]', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'An unexpected error occurred while processing your file. Please try again.',
        errorType: UploadErrorType.SERVER_ERROR
      } as UploadResponse,
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to upload files.' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to upload files.' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to upload files.' },
    { status: 405 }
  );
}